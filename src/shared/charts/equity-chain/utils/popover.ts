import Vue, { Ref, ref } from 'vue';
import { Point } from 'yfiles';

const getPosition = (triggerRect, popover, container, gap = 5) => {
  const popoverRect = popover.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  const spaceRight = containerRect.width - triggerRect.right;
  const spaceBottom = containerRect.height - triggerRect.bottom;
  const spaceLeft = triggerRect.left;
  const spaceTop = triggerRect.top;

  const position = { top: triggerRect.top, left: triggerRect.left + triggerRect.width + gap };

  if (spaceRight < popoverRect.width && spaceLeft >= popoverRect.width) {
    position.left = triggerRect.left - popoverRect.width - gap;
  }

  if (spaceBottom < popoverRect.height && spaceTop >= popoverRect.height) {
    position.top = position.top - (popoverRect.height - spaceBottom);
  }

  return position;
};

export const usePopover = (parentDom: Ref<HTMLElement | null>) => {
  const timer = ref<number>();
  const container = ref<HTMLElement | null>();

  const createContainer = (x = 0, y = 0) => {
    if (container.value) return;
    const div = document.createElement('div');
    div.style.position = 'absolute';
    div.style.top = `${y}px`;
    div.style.left = `${x}px`;
    container.value = div;
  };

  const move = ({ nodeItem, graphComponent }) => {
    if (!container.value) return;
    const { x, y, width, height, bottomRight, topLeft } = nodeItem.layout;
    const topLeftPoint = graphComponent.toViewCoordinates(new Point(topLeft.x, topLeft.y));
    const bottomRightPoint = graphComponent.toViewCoordinates(new Point(bottomRight.x, bottomRight.y));
    const position = getPosition(
      {
        left: topLeftPoint.x,
        top: topLeftPoint.y,
        right: bottomRightPoint.x,
        bottom: bottomRightPoint.y,
        width,
        height,
      },
      container.value,
      parentDom.value
    );

    container.value.style.top = `${position.top}px`;
    container.value.style.left = `${position.left}px`;
  };

  const createContent = (comp, configs: Record<string, any> = {}) => {
    if (!container.value || !parentDom.value) return;
    const Ctor: any = Vue.extend(comp);
    const instance = new Ctor({
      propsData: configs.props,
      data() {
        return configs.data;
      },
    });
    instance.$mount();
    container.value.appendChild(instance.$el);
    parentDom.value.appendChild(container.value);
  };

  const unload = () => {
    timer.value = setTimeout(() => {
      if (!container.value || !parentDom.value) {
        timer.value && clearTimeout(timer.value);
        return;
      }
      if (parentDom.value?.contains(container.value)) {
        parentDom.value.removeChild(container.value);
        container.value = null;
      }
    }, 300);
  };

  const load = (comp, configs: Record<string, any> = {}) => {
    createContainer();
    createContent(comp, configs);
    container.value?.addEventListener('mouseover', () => {
      timer.value && clearTimeout(timer.value);
    });
    container.value?.addEventListener('mouseout', () => {
      unload();
    });
  };

  return {
    load,
    unload,
    move,
  };
};
